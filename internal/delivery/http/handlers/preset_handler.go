package handlers

import (
	"dexmarket-ws/global"
	presetService "dexmarket-ws/internal/service/preset"
	walletService "dexmarket-ws/internal/service/wallet"
	"dexmarket-ws/internal/service/wallet/dtos"
	"strconv"

	"github.com/gin-gonic/gin"
)

// PresetHandler handles HTTP requests for preset-related operations
type PresetHandler struct {
	BaseHandler
	walletService walletService.IWalletService
}

// NewPresetHandler creates a new preset handler
func NewPresetHandler(presetService presetService.IPresetService) *PresetHandler {
	return &PresetHandler{
		presetService: presetService,
	}
}

// GetPresets handles retrieval of user presets
func (h *PresetHandler) GetPresets(c *gin.Context) {
	userID := c.GetInt64(global.UserIDKey)
	presets, err := h.presetService.GetPresets(c, userID)
	if err != nil {
		h.HandleError(c, err)
		return
	}

	h.HandleSuccess(c, presets)
}

func (h *PresetHandler) CreatePreset(c *gin.Context) {
	var req dtos.CreatePresetInput
	if err := c.ShouldBindJSON(&req); err != nil {
		h.HandleError(c, err)
		return
	}

	preset, err := h.presetService.CreatePreset(c, req.UserID)
	if err != nil {
		h.HandleError(c, err)
		return
	}

	h.HandleSuccess(c, preset)
}

func (h *PresetHandler) UpdatePresetName(c *gin.Context) {
	userID := c.GetInt64(global.UserIDKey)

	var dto dtos.UpdatedPresetDto
	dto.UserID = userID
	presetID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		h.HandleError(c, err)
		return
	}

	dto.PresetID = presetID

	if err := c.ShouldBindJSON(&dto); err != nil {
		h.HandleError(c, err)
		return
	}

	preset, err := h.presetService.UpdatePresetName(c, dto)
	if err != nil {
		h.HandleError(c, err)
		return
	}

	h.HandleSuccess(c, preset)
}

package handlers

import (
	"dexmarket-ws/global"
	walletService "dexmarket-ws/internal/service/wallet"
	"dexmarket-ws/internal/service/wallet/dtos"
	"strconv"

	"github.com/gin-gonic/gin"
)

// WalletHandler handles HTTP requests for wallet-related operations
type WalletHandler struct {
	BaseHandler
	walletService walletService.IWalletService
}

// NewWalletHandler creates a new wallet handler
func NewWalletHandler(walletService walletService.IWalletService) *WalletHandler {
	return &WalletHandler{
		walletService: walletService,
	}
}

// GetWallets handles retrieval of user wallets
func (h *WalletHandler) GetWallets(c *gin.Context) {
	userID := c.GetInt64(global.UserIDKey)
	wallets, err := h.walletService.GetWallets(c, userID)
	if err != nil {
		h.HandleError(c, err)
		return
	}

	h.HandleSuccess(c, wallets)
}

func (h *WalletHandler) CreateWallet(c *gin.Context) {
	var req dtos.CreateWalletInput
	if err := c.ShouldBindJSON(&req); err != nil {
		h.HandleError(c, err)
		return
	}

	wallet, err := h.walletService.CreateWallet(c, req.UserID)
	if err != nil {
		h.HandleError(c, err)
		return
	}

	h.HandleSuccess(c, wallet)
}

func (h *WalletHandler) UpdateWalletName(c *gin.Context) {
	userID := c.GetInt64(global.UserIDKey)

	var dto dtos.UpdatedWalletDto
	dto.UserID = userID
	walletID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		h.HandleError(c, err)
		return
	}

	dto.WalletID = walletID

	if err := c.ShouldBindJSON(&dto); err != nil {
		h.HandleError(c, err)
		return
	}

	wallet, err := h.walletService.UpdateWalletName(c, dto)
	if err != nil {
		h.HandleError(c, err)
		return
	}

	h.HandleSuccess(c, wallet)
}

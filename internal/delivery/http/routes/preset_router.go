package routes

import (
	"dexmarket-ws/internal/delivery/http/handlers"

	"github.com/gin-gonic/gin"
)

// PresetRouter manages preset-related routes
type PresetRouter struct {
	presetHandler *handlers.PresetHandler
}

// NewPresetRouter creates a new preset router
func NewPresetRouter(presetHandler *handlers.PresetHandler) *PresetRouter {
	return &PresetRouter{
		presetHandler: presetHandler,
	}
}

// Setup sets up all preset-related routes
func (r *PresetRouter) Setup(router *gin.RouterGroup) {
	presetGroup := router.Group("/presets")
	{
		presetGroup.GET("", r.presetHandler.GetPresets)
		presetGroup.POST("", r.presetHandler.CreatePreset)
		presetGroup.PUT(":id", r.presetHandler.UpdatePresetName)

	}
}

package preset

import (
	"context"
	"dexmarket-ws/internal/domain/preset"
	"dexmarket-ws/internal/domain/user"
	"dexmarket-ws/internal/service/preset/dtos"
	"fmt"
)

const MAX_PRESET_PER_USER = 3

type IPresetService interface {
	CreatePreset(ctx context.Context, dto dtos.CreatePresetInput) (*dtos.PresetDTO, error)
	GetPresets(ctx context.Context, userId int64) (*dtos.PresetResponseDTO, error)
	UpdatePresetName(ctx context.Context, dto dtos.UpdatedPresetDto) (*dtos.PresetDTO, error)
}

type presetServiceImpl struct {
	presetService preset.PresetService
	userService   user.IUserService
}

func (ps *presetServiceImpl) CreatePreset(ctx context.Context, userId int64) (*dtos.PresetDTO, error) {
	_, errUser := ps.userService.GetExistUserData(ctx, userId)
	if errUser != nil {
		return nil, errUser
	}

	presets, errPresets := ps.presetService.GetPreset(ctx, userId)
	if errPresets != nil {
		return nil, errPresets
	}

	if len(presets) >= MAX_PRESET_PER_USER {
		return nil, fmt.Errorf("user cannot have more than %d presets", MAX_PRESET_PER_USER)
	}
	return nil, nil
}

package postgres

import (
	"context"
	"dexmarket-ws/internal/database"
	domainPreset "dexmarket-ws/internal/domain/preset"
	domainWallet "dexmarket-ws/internal/domain/wallet"
	"errors"
	"log"
	"time"
)

// presetRepository implements the preset.Repository interface
type presetRepository struct {
	db *database.Queries
}

// NewPresetRepository creates a new wallet repository
func NewPresetRepository(db *database.Queries) domainPreset.PresetRepo {
	return &presetRepository{
		db: db,
	}
}

// FindByUserID finds presets by user ID
func (r *presetRepository) FindByUserID(ctx context.Context, userID int64) ([]domainPreset.Preset, error) {
	presets, err := r.db.FindPresetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	result := make([]domainPreset.Preset, len(presets))
	for i, p := range presets {
		result[i] = domainWallet.ToPreset(p)
	}
	return result, nil
}

// Save saves a wallet
func (r *presetRepository) CreatePreset(ctx context.Context, createPresetDto *domainPreset.Preset) ([]domainPreset.Preset, error) {
	presetParams := database.CreatePresetParams{
		UserID:        createWalletDto.UserID,
		PrivyWalletID: createWalletDto.PrivyWalletId,
		PublicKey:     createWalletDto.PublicKey,
		Name:          createWalletDto.Name,
		CreatedAt:     time.Now().Unix(),
		UpdatedAt:     time.Now().Unix(),
	}
	presets, err := r.db.CreatePreset(ctx, presetParams)
	if err != nil {
		log.Printf("[ERROR] %v", err)
		return nil, errors.New("error creating PresetRepo")
	}
	var result []domainPreset.Preset
	for _, p := range presets {
		result = append(result, domainWallet.ToPreset(p))
	}
	return result, nil
}

func (r *presetRepository) UpdatePreset(ctx context.Context, preset *domainPreset.Preset) (domainPreset.Preset, error) {
	presetParams := database.UpdatePresetNameParams{
		ID:        preset.ID,
		Name:      preset.Name,
		UpdatedAt: time.Now().Unix(),
	}

	updatedPreset, err := r.db.UpdateWalletName(ctx, presetParams)
	if err != nil {
		return domainPreset.Preset{}, err
	}
	result := domainPreset.ToPreset(updatedPreset)
	return result, nil
}

package wallet

import (
	"dexmarket-ws/internal/database"
	"time"
)

// Wallet represents a cryptocurrency wallet entity in the domain
type Wallet struct {
	ID            int64  `json:"id"`
	UserID        int64  `json:"userId"`
	PublicKey     string `json:"publicKey"`
	PrivyWalletId string `json:"privyWalletId"`
	Name          string `json:"name"`
	CreatedAt     int64  `json:"createdAt"`
	UpdatedAt     int64  `json:"updatedAt"`
}

// NewWallet creates a new wallet entity with the given parameters
func NewWallet(userID int64, publicKey string, privyWalletId string, name string) *Wallet {
	now := time.Now().Unix()
	return &Wallet{
		UserID:        userID,
		PublicKey:     publicKey,
		PrivyWalletId: privyWalletId,
		Name:          name,
		CreatedAt:     now,
		UpdatedAt:     now,
	}
}

// ToWallet converts a database model to a domain entity
func ToWallet(in database.DexmarketWsUserWallet) Wallet {
	return Wallet{
		ID:            in.ID,
		UserID:        in.UserID,
		PublicKey:     in.PublicKey,
		PrivyWalletId: in.PrivyWalletID,
		Name:          in.Name,
		CreatedAt:     in.CreatedAt,
		UpdatedAt:     in.UpdatedAt,
	}
}

// ToWalletDatabase converts a domain entity to a database model
func ToWalletDatabase(in Wallet) database.DexmarketWsUserWallet {
	return database.DexmarketWsUserWallet{
		ID:            in.ID,
		UserID:        in.UserID,
		PublicKey:     in.PublicKey,
		PrivyWalletID: in.PrivyWalletId,
		Name:          in.Name,
		CreatedAt:     in.CreatedAt,
		UpdatedAt:     in.UpdatedAt,
	}
}

// GetID returns the wallet's ID
func (w Wallet) GetID() int64 {
	return w.ID
}

// GetUserID returns the wallet's user ID
func (w Wallet) GetUserID() int64 {
	return w.UserID
}

// GetPublicKey returns the wallet's public key
func (w Wallet) GetPublicKey() string {
	return w.PublicKey
}

// Get PrivyWalletId returns the wallet's PrivyWalletId
func (w Wallet) GetPrivyWalletId() string {
	return w.PrivyWalletId
}

// GetName returns the wallet's name
func (w Wallet) GetName() string {
	return w.Name
}

// GetCreatedAt returns the wallet's creation timestamp
func (w Wallet) GetCreatedAt() int64 {
	return w.CreatedAt
}

// GetUpdatedAt returns the wallet's last update timestamp
func (w Wallet) GetUpdatedAt() int64 {
	return w.UpdatedAt
}

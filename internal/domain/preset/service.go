package preset

import "context"

type PresetService interface {
	CreatePreset(ctx context.Context, preset *Preset) ([]Preset, error)
	GetPreset(ctx context.Context, userId int64) ([]Preset, error)
	UpdatePresetName(ctx context.Context, preset *Preset) (Preset, error)
}

type presetServiceImpl struct {
	presetRepo PresetRepo
}

func NewPresetService(presetRepo PresetRepo) PresetService {
	return &presetServiceImpl{presetRepo: presetRepo}
}

func (s *presetServiceImpl) CreatePreset(ctx context.Context, preset *Preset) ([]Preset, error) {
	return s.presetRepo.CreatePreset(ctx, preset)
}

func (s *presetServiceImpl) GetPreset(ctx context.Context, userId int64) ([]Preset, error) {
	return s.presetRepo.FindByUserID(ctx, userId)
}

func (s *presetServiceImpl) UpdatePresetName(ctx context.Context, preset *Preset) (Preset, error) {
	return s.presetRepo.UpdatePreset(ctx, preset)
}

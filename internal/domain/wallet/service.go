package wallet

import "context"

type WalletService interface {
	CreateWallet(ctx context.Context, wallet *Wallet) ([]Wallet, error)
	GetWallet(ctx context.Context, userId int64) ([]Wallet, error)
	UpdateWalletName(ctx context.Context, wallet *Wallet) (Wallet, error)
}

type walletServiceImpl struct {
	walletRepo WalletRepo
}

func NewWalletService(walletRepo WalletRepo) WalletService {
	return &walletServiceImpl{walletRepo: walletRepo}
}

func (s *walletServiceImpl) CreateWallet(ctx context.Context, wallet *Wallet) ([]Wallet, error) {
	return s.walletRepo.CreateWallet(ctx, wallet)
}

func (s *walletServiceImpl) GetWallet(ctx context.Context, userId int64) ([]Wallet, error) {
	return s.walletRepo.FindByUserID(ctx, userId)
}

func (s *walletServiceImpl) UpdateWalletName(ctx context.Context, wallet *Wallet) (Wallet, error) {
	return s.walletRepo.UpdateWallet(ctx, wallet)
}
